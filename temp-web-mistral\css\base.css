/* ===== PENSION MISTRAL - BASE STYLES ===== */

/* ===== FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@500&family=Noto+Color+Emoji&family=Pacifico&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;1,100;1,200;1,300;1,400&family=Roboto+Flex:opsz,wght@8..144,100;8..144,200;8..144,300;8..144,400;8..144,500&family=Rouge+Script&family=Send+Flowers&display=swap');

/* ===== RESET ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* ===== LAYOUT UTILITIES ===== */
.row {
  max-width: 1140px;
  margin: 0 auto;
}

.clearfix {
  zoom: 1;
}

.clearfix:after {
  content: ".";
  clear: both;
  display: block;
  height: 0;
  visibility: hidden;
}

/* ===== SPACING UTILITIES ===== */
.empty-space-zero-px { height: 0px; }
.empty-space-ten-px { height: 10px; }
.empty-space-twfive-px { height: 25px; }
.empty-space-thfive-px { height: 35px; }
.empty-space-fifty-px { height: 50px; }

/* ===== SECTION TITLES ===== */
.title-main {
  max-width: 80%;
  text-decoration: none;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 1.5rem;
  text-align: center; 
  margin: 1.5rem auto;
  padding: 0.5rem; 
  text-shadow: 1px 1px 3px rgb(96, 96, 96);
  box-shadow: 4px 6px 10px rgb(96, 96, 96);
  border: 1px solid rgb(0, 255, 255);
  border-radius: 5px;
}

.title-main h3 {
  font-size: 3rem;
  padding: 0 1rem;
}

/* ===== HERO SECTION BASE ===== */
header {
  background: linear-gradient(rgba(0,0,0,0.5),rgba(0, 0, 0, 0.5)), url("../img/background/agios-nikitas_2272x1000px.jpg");
  background-size: cover;
  height: 100vh;
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
  box-sizing: border-box;
}

#logo-turtle {
  max-width: 250px;
  position: absolute;
  top: 35%;
  left: 50%;
  transform: translate(-50%,-50%);
}

header article { 
  width: 80%;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%,0%);
}

article h1 {
  max-width: 100%;
  font-family: "Send Flowers"; 
  color: #fff;
  text-align: center;
  margin-top: 40px;
  font-size: 80px;
}

article h2 {
  font-family: Roboto Flex;
  color: rgb(0, 255, 255);
  font-size: 35px;
  text-align: center;
  text-transform: uppercase;
}

article h4 {
  font-family: Arial, Helvetica, sans-serif;
  color: #fff;
  font-size: 20px;
  text-align: center;
  margin-top: 2%;
  line-height: normal;
}

/* ===== MAIN CONTENT BASE ===== */
.post-content-pension-see {
  display: flex;
  width: 80%;
  font-family: Arial, Helvetica, sans-serif;
  margin: 1em auto 2em auto;
  padding: 2px 2px;
}

.post-content-pension-see p {
  line-height: 1.3em;
  padding: 0.5em;
}

article .post-content-pension-see p {
  padding: 0 2em;  
}

.pension-mistral-p p {
  font-size: 1rem;
  line-height: 1.6;
}

.turtle-content-pension {
  float: right;
  width: 160px;
  padding: -5px;
  margin-bottom: 10px;
}

/* ===== GALLERY BASE ===== */
.gallery-pension-mistral {
  max-width: 100%;
  margin: auto;
  padding: 20px 0 20px 0;
}

.frame-gallery-pension-mistral {
  max-width: 100%;
  display: flex;
}

.title-gallery-p-mistral {
  text-decoration: none;
  color: #000;
  font-family: Arial, Helvetica, sans-serif;
  margin: 5% 15%;
  text-shadow: 1px 1px 3px rgb(96 96 96);
  text-align: center;
  border: 1px solid rgb(0, 255, 255);
  border-radius: 0.2em;
  box-shadow: 1px 1px 5px rgb(96 96 96);
}

.one-image-frame img {
  display: block;
  margin: 25px auto 25px auto;
  letter-spacing: 0.3px;
  box-shadow: 4px 6px 10px rgb(96, 96, 96);
  border: 1px solid rgb(0, 255, 255);
  border-radius: 5px;
}

.gallery-pension-mistral-grid {
  padding: 20px;
}

.gallery-pension-mistral-grid .cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.gallery-pension-mistral-grid .card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.gallery-pension-mistral-grid .card:hover {
  transform: translateY(-5px);
}

/* ===== ASIDE SECTIONS BASE ===== */
.aside-gallery-ag-nikitas {
  background: linear-gradient(rgba(0,0,0,0.5),rgba(0, 0, 0, 0.5)), url("../img/background/agios-nikitas_2_1920-845px.jpg");
  background-size: cover;
  background-attachment: fixed;
}

.aside-image-frame {
  padding: 100px 20px;
  justify-content: center;
}

.aside-image-frame img {
  max-width: 100%;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-0%);
  border: 5px solid rgb(255, 255, 255);
}

/* ===== FOOTER BASE ===== */
.footer-pension-mistral {
  height: 50px;
  display: block;
  text-align: center;
  background: #053981;
}

.footer-pension-mistral p {
  text-decoration: none;
  padding-top: 15px;
  font-family: Arial, Helvetica, sans-serif;
  color: #fff;
}

/* ===== CONTACT FORM BASE ===== */
.contact-form-text-h1,
.contact-left-side h1 {
  font-size: 4em;
}

.frame-map {
  width: 500px;
  height: 300px;
}

form {
  width: 500px;
  max-width: 100%;
}
