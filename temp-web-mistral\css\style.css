/* @import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@500&family=Pacifico&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;1,100;1,200;1,300;1,400&family=Roboto+Flex:opsz,wght@8..144,100;8..144,200;8..144,300;8..144,400;8..144,500&family=Rouge+Script&family=Send+Flowers&display=swap'); */
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@500&family=Noto+Color+Emoji&family=Pacifico&family=Raleway:ital,wght@0,100;0,200;0,300;0,400;1,100;1,200;1,300;1,400&family=Roboto+Flex:opsz,wght@8..144,100;8..144,200;8..144,300;8..144,400;8..144,500&family=Rouge+Script&family=Send+Flowers&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}


/* REUSABLE */
.row {
    max-width: 1140px;
    margin: 0 auto;
  }
  

/* --- */
/* *** empty space *** */
.empty-space-zero-px {
    height: 0px;
  }
  .empty-space-ten-px {
    height: 10px;
  }
  .empty-space-twfive-px {
    height: 25px;
  }
  .empty-space-thfive-px {
    height: 35px;
  }
  .empty-space-fifty-px {
    height: 50px;
  }
    /* *** empty space ending *** */


/* --- */
/* *** title section *** */
  .title-main {
      max-width: 80%;
      text-decoration: none;
      font-family: Arial, Helvetica, sans-serif;
      font-size: 1.5rem;
      text-align: center; 
      margin: 1.5rem auto;
      padding: 0.5rem; 
      text-shadow: 1px 1px 3px rgb(96, 96, 96);
      box-shadow: 4px 6px 10px rgb(96, 96, 96);
      border: 1px solid rgb(0, 255, 255);
      border-radius: 5px;
    } 
    /* *** title section ending *** */


/* --- */
/*** * Stop floating * ***/
  .clearfix {zoom: 1}
  .clearfix:after {
      content: ".";
      clear: both;
      display: block;
      height: 0;
      visibility: hidden;
      }
    /*** * Stop floating  ending* ***/

/* header - background */
    header {
        background: linear-gradient(rgba(0,0,0,0.5),rgba(0, 0, 0, 0.5)), url("../img/background/agios-nikitas_2272x1000px.jpg");
        background-size: cover;
        height: 100vh;
        background-attachment: fixed;
        background-position: center;
        background-repeat: no-repeat;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding-top: 100px;
        box-sizing: border-box;
  }

    /* *** HEADER logo turtle *** */

    #logo-turtle {
        max-width: 250px;
        position: absolute;
        top: 35%;
        left: 50%;
        transform: translate(-50%,-50%);
        }


        header article { 
            width: 80%;
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%,0%);
            /* border: 1px solid red; */
        }

        article h1 {
            max-width: 100%;
            font-family: "Send Flowers"; 
            color: #fff;
            text-align: center;
            margin-top: 40px;
            font-size: 80px;
            
        }

        article h2 {
            font-family: Roboto Flex;
            color: rgb(0, 255, 255);
            font-size: 35px;
            text-align: center;
            text-transform: uppercase;
            /* border: 1px solid red; */
        }

        article h4 {
            font-family: Arial, Helvetica, sans-serif;
            color: #fff;
            font-size: 20px;
            text-align: center;
            margin-top: 2%;
            line-height: normal;
            /* border: 1px solid red; */
        }



/* ************** * MAIN -pension mistral * ************** */


.post-content-pension-see  {
    display: flex;
    width: 80%;
    font-family: Arial, Helvetica, sans-serif;
    margin: 1em auto 2em auto;
    padding: 2px 2px;
    
}

.post-content-pension-see p {
    line-height: 1.3em;
    /* border: 1px solid red; */
    padding: 0.5em;
}

article .post-content-pension-see p {
    padding: 0 2em;  
}



.turtle-content-pension {
  float: right;
  width: 160px;
  padding: -5px;
  margin-bottom: 10px;
  /* border: 1px solid red; */

}

/* gallery-pension-mistral */

.gallery-pension-mistral {
    max-width: 100%;
    margin: auto;
    padding: 20px 0 20px 0;
    /* border: 1px solid red; */
}

.frame-gallery-pension-mistral {
    max-width: 100%;
    display: flex;
    /* border: 1px solid red; */
}

.title-gallery-p-mistral {
    text-decoration: none;
    color: #000;
    font-family: Arial, Helvetica, sans-serif;
    margin: 5% 15%;
    text-shadow: 1px 1px 3px rgb(96 96 96);
    text-align: center;
    border: 1px solid rgb(0, 255, 255);
    border-radius: 0.2em;
    box-shadow: 1px 1px 5px rgb(96 96 96);
}
.one-image-frame img {
    display:block;
    margin: 25px auto 25px auto;
    letter-spacing: 0.3px;
    box-shadow: 4px 6px 10px rgb(96, 96, 96);
    border: 1px solid rgb(0, 255, 255);
    border-radius: 5px;
    /* border: 1px solid red; */
}

/* aside - Ag. Nikitas */

.aside-gallery-ag-nikitas {
  background: linear-gradient(rgba(0,0,0,0.5),rgba(0, 0, 0, 0.5)), url("../img/background/agios-nikitas_2_1920-845px.jpg");
  background-size: cover;
  background-attachment: fixed;
}


.aside-image-frame {
    padding: 100px 20px;
    justify-content: center;
    /* border: 1px solid red; */
    /* margin: 25px 0 25px 0;   */
}

.aside-image-frame img {
  max-width: 100%;
  position: relative;
  top: 50%;
  left: 50%;
  transform: translate(-50%,-0%);
  border: 5px solid rgb(255, 255, 255);
}


/* footer */

.footer-pension-mistral {
  height: 50px;
  display: block;
  text-align: center;
  background: #053981;
}
.footer-pension-mistral p {
  text-decoration: none;
 padding-top: 15px;
  font-family: Arial, Helvetica, sans-serif;
  color: #fff;

}

/* ===== RESPONSIVE HERO SECTION ===== */

/* Tablet and smaller desktop */
@media (max-width: 1024px) {
    header {
        height: 92h;
        background-attachment: scroll;
    }

    #logo-turtle {
        max-width: 192px;
        top: 45%;
    }

    article h1 {
        margin-top: 80px;
        font-size: 62px;
    }

    article h2 {
        font-size: 25px;
    }

    article h4 {
        font-size: 16px;
    }
}

/* Mobile landscape */
@media (max-width: 768px) and (orientation: landscape) {
    header {
        height: 100vh;
        background-attachment: scroll;
    }

    #logo-turtle {
        max-width: 192px;
        top: 45%;
    }

    header article {
        top: 45%;
        width: 90%;
    }

    article h1 {
        font-size: 3rem;
    }

    article h2 {
        font-size: 2rem;
    }

    article h4 {
        font-size: 1rem;
        margin-top: 1rem;
    }
}

/* Mobile portrait */
@media (max-width: 768px) and (orientation: portrait) {
    header {
        height: 100vh;
        background-attachment: scroll;
        min-height: 600px;
    }

    #logo-turtle {
        max-width: 192px;
        width: 240px;
        top: 35%;
    }

    header article {
        top: 42%;
        width: 90%;
    }

    article h1 {
        font-size: 64px;
        margin-top: 50px;
        line-height: 1.2;
        padding: 0 1rem;
    }

    article h2 {
        font-size: 40px;
        
    }

    article h4 {
        font-size: 16px;
        margin-top: 16px;
        margin-bottom: 1rem;
        line-height: 1.3;
        padding: 0 1rem;
    }
}

/* Tablet portrait */
@media (max-width: 1024px) and (orientation: portrait) {
    header {
        height: 100vh;
        min-height: 600px;
        background-attachment: scroll;
    }

    #logo-turtle {
rem;
        margin-top: 1rem;
        line-height: 1.3;
    }
}

/* Small mobile */
@media (max-width: 480px) {
    header {
        height: 100vh;
        min-height: 500px;
        background-attachment: scroll;
    }

    #logo-turtle {
        max-width: 192px;
        width: 30rem;
        top: 45%;
    }

    header article {
        top: 42%;
        width: 95%;
    }

    article h1 {
        font-size: 2.5rem;
        margin-top: 0.5rem;
    }

    article h2 {
        font-size: 1.8rem;
        margin-top: 0.5rem;
    }

    article h4 {
        font-size: 1rem;
        margin-top: 0.8rem;
        line-height: 1.3;
        padding: 0 1rem;
    }
}

/* Very small mobile */
@media (max-width: 360px) {
    header {
        min-height: 450px;
    }

    #logo-turtle {
        max-width: 10rem;
        top: 16%;
    }

    header article {
        top: 40%;
    }

    article h1 {
        font-size: 2rem;
    }

    article h2 {
        font-size: 1.5rem;
    }

    article h4 {
        font-size: 0.9rem;
        padding: 0 0.5rem;
    }
}

/* ===== RESPONSIVE CONTENT SECTIONS ===== */

@media (max-width: 768px) {
    .post-content-pension-see {
        width: 95%;
        flex-direction: column;
    }

    .turtle-content-pension {
        float: none;
        width: 120px;
        margin: 0 auto 15px auto;
        display: block;
    }

    .aside-gallery-ag-nikitas {
        background-attachment: scroll;
    }

    .aside-image-frame {
        padding: 50px 10px;
    }

    .aside-image-frame img {
        max-width: 90%;
        border: 3px solid rgb(255, 255, 255);
    }
}

@media (max-width: 480px) {
    .post-content-pension-see p {
        padding: 0.3em;
        font-size: 14px;
        line-height: 1.4;
    }

    .turtle-content-pension {
        width: 100px;
    }

    .aside-image-frame {
        padding: 30px 5px;
    }

    .aside-image-frame img {
        max-width: 95%;
        border: 2px solid rgb(255, 255, 255);
    }
}

/* ===== RESPONSIVE GALLERY SECTIONS ===== */

/* Modern CSS Grid for gallery */
.gallery-pension-mistral-grid {
    padding: 20px;
}

.gallery-pension-mistral-grid .cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.gallery-pension-mistral-grid .card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-pension-mistral-grid .card:hover {
    transform: translateY(-5px);
}

.one-image-frame img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.one-image-frame img:hover {
    transform: scale(1.05);
}

/* Tablet landscape */
@media (max-width: 1024px) {
    .gallery-pension-mistral-grid .cards {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        padding: 0 20px;
    }

    .title-main h3 {
        font-size: 2.5rem;
        padding: 0 20px;
    }
}

/* Tablet portrait */
@media (max-width: 768px) {
    .gallery-pension-mistral-grid {
        padding: 15px;
    }

    .gallery-pension-mistral-grid .cards {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 10px;
    }

    .title-main h3 {
        font-size: 2rem;
        padding: 0 15px;
        text-align: center;
    }

    .title-gallery-p-mistral {
        margin: 3% 10%;
        font-size: 1.2rem;
    }
}

/* Mobile */
@media (max-width: 480px) {
    .gallery-pension-mistral-grid {
        padding: 10px;
    }

    .gallery-pension-mistral-grid .cards {
        gap: 1rem;
        padding: 0 5px;
    }

    .title-main h3 {
        font-size: 1.8rem;
        padding: 0 10px;
    }

    .title-gallery-p-mistral {
        margin: 2% 5%;
        font-size: 1.1rem;
        padding: 8px;
    }

    .title-gallery-p-mistral h4 {
        font-size: 1.1rem;
        margin: 0;
    }
}

/* Very small mobile */
@media (max-width: 360px) {
    .title-main h3 {
        font-size: 1.6rem;
    }

    .title-gallery-p-mistral {
        margin: 2% 3%;
        font-size: 1rem;
        padding: 6px;
    }
}
