(function($){
    $(function(){

                 // scrolling to top menu home
         $(".jq--scroll-home-top").click(function(){
           $("html, body").animate({scrollTop: $(".jq--home-top").offset().top}, 1400);
          });
                   // scrolling to top menu pension mistral
         $(".jq--scroll-pension-see").click(function(){
           $("html, body").animate({scrollTop: $(".jq--pension-see").offset().top}, 800);
         });
 
                   // crolling to top menu photos
         $(".jq--scroll-photos-see").click(function(){
           $("html, body").animate({scrollTop: $(".jq--photos-see").offset().top}, 1000);
         });
 
                   // // crolling to top menu agios nikitas
         $(".jq--scroll-agnikitas-see").click(function(){
           $("html, body").animate({scrollTop: $(".jq--agnikitas-see").offset().top}, 1200);
         });
 
                // // crolling to top menu contact
         $(".jq--scroll-contact").click(function(){
           $("html, body").animate({scrollTop: $(".jq--contact").offset().top}, 1400);
         });


 
 
 // mobile navigation 
         $(".jq--nav-icon").click(function(){
 
           $(".nav-background").slideToggle();
           $(".mobile-nav-back").fadeToggle();
           $("nav ul").fadeToggle();
   
         });
 
         /* change hamburger to cross vice versa */
         $(".jq--image-hamburger").click(function(){
   
           if($(".jq--image-hamburger").attr("src") == "img/hamburgerMenu.png")
           {
               $($(".jq--image-hamburger").attr("src","img/crossMenu.png"));
           } 
           else 
           {
               $($(".jq--image-hamburger").attr("src","img/hamburgerMenu.png"));
           }  
       });
        
    });
 })(jQuery);