/* ************************************************************************ */
/*
@media only screen and (max-width: 1100px){
    #logo_nav_txt {
        font-size: 40px;
        transform: translate(-50%,-0%);
    }

    #calling-phone-number-nav {
        transform: translate(185%,-50%);
        font-size: 20px;
        letter-spacing: 0.1px;
    }

    #logo-flower-nav {
        width: 75px;
        transform: translate( 690%,-40%);
    }


}
*/

/* ************************************************************************ */
/*
@media only screen and (max-width: 1000px){
    #logo_nav_txt {
        font-size: 35px;
        transform: translate(-50%,-5%);
    }

    #calling-phone-number-nav {
        transform: translate(200%,-50%);
        font-size: 18px;
        letter-spacing: 0.1px;
    }

    #logo-flower-nav {
        width: 75px;
        transform: translate( 670%,-40%);
    }


}*/

/* ************************************************************************ */

@media only screen and (max-width: 950px){
    #logo_nav_txt {
        font-size: 20px;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(50%,20%);
        font-size: 15px;
        letter-spacing: 0.1px;
    }

    #logo-flower-nav {
        width: 75px;
        transform: translate( 570%,-40%);
    }


    #logo-turtle {
        width:  380px;
        margin-top: 30px;

    }
    article h1 {
        font-size: 5rem;
        margin-top: 30px;
    }
    article h2 {
        font-size: 3rem;
        margin-top: 10px;
    }
    article h4 {
        font-size: 1.7rem;
        margin-top: 20px;
    }

    .one-image-frame img {
    width: 250px;
    }
    .aside-image-frame img {
    width: 500px;
    }

    #logo-navigation {
        max-width: 50px;
    }
    #calling-phone-number-nav {
        font-size: 0.9em;
        text-decoration: none;
        letter-spacing: 0px;
        border: 1px solid transparent;
    }
}




/* ************************************************************************ */
@media only screen and (max-width: 820px){
    #logo_nav_txt {
        font-size: 20px;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(50%,20%);
        font-size: 15px;
        letter-spacing: 0.1px;
    }

    #logo-flower-nav {
        width: 75px;
        transform: translate( 505%,-40%);
    }


    #logo-turtle {
        width:  400px;
        margin-top: 30px;

    }
    article h1 {
        font-size: 6rem;
        margin-top: 30px;
    }
    article h2 {
        font-size: 3rem;
        margin-top: 10px;
    }
    article h4 {
        font-size: 1.7rem;
        margin-top: 20px;
    }

    .one-image-frame img {
    width: 250px;
    }
    .aside-image-frame img {
    width: 500px;
    }

    #logo-navigation {
        max-width: 50px;
    }
    #calling-phone-number-nav {
        font-size: 0.9em;
        text-decoration: none;
        letter-spacing: 0px;
        border: 1px solid transparent;
    }
}


/* ************************************************************************ */
@media only screen and (max-width: 800px) and (orientation: landscape) {
    #logo_nav_txt {
        font-size: 1.25rem;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(48%,25%);
    }

    #logo-turtle {
        width: 150px;
        top: 35%;
    }
    #logo-flower-nav {
        width: 85px;
        transform: translate( 385%,-40%);
    }

    .title-main {
        max-width: 95%;
    }

    header article {
        width: 95%;
        top: 50%;
    }
    .post-content-pension-see {
        width: 100%;
    }

    .gallery-pension-mistral img {
        width: 70%;
    }

    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 80%;
        margin: 5% auto 5% auto;
    }
    article h1 {
        font-size: 4rem;
    }
    article h2 {
        font-size: 2rem;
    }
    article h4 {
        font-size: 1rem;
    }

}




/* ************************************************************************ */
@media only screen and (max-width: 768px){
    #logo-turtle {
        width:  240px;
        margin-top: 30px;

    }
    article h1 {
        font-size: 2.8rem;
        margin-top: 20px;
    }
    article h2 {
        font-size: 1.8rem;
        margin-top: 10px;
    }
    article h4 {
        font-size: 0.7rem;
        margin-top: 10px;
    }

    .one-image-frame img {
    width: 250px;
    }
    .aside-image-frame img {
    width: 270px;
    }

    #logo-navigation {
        max-width: 50px;
    }
    #calling-phone-number-nav {
        font-size: 0.9em;
        text-decoration: none;
        letter-spacing: 0px;
        border: 1px solid transparent;
    }
}



/* ************************************************************************ */
@media only screen and (max-width: 668px) and (orientation: landscape) {
    #logo_nav_txt {
        font-size: 1.25rem;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(48%,25%);
    }

    #logo-turtle {
        width: 150px;
        top: 35%;
    }
    #logo-flower-nav {
        width: 85px;
        transform: translate( 385%,-40%);
    }

    .title-main {
        max-width: 95%;
    }

    header article {
        width: 95%;
        top: 50%;
    }
    .post-content-pension-see {
        width: 100%;
    }

    .gallery-pension-mistral img {
        width: 70%;
    }

    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 80%;
        margin: 5% auto 5% auto;
    }
    article h1 {
        font-size: 4rem;
    }
    article h2 {
        font-size: 2rem;
    }
    article h4 {
        font-size: 1rem;
    }



}


/* ************************************************************************ */
@media only screen and (max-width: 616px) and (orientation: landscape) {
    #logo_nav_txt {
        font-size: 1.25rem;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(48%,25%);
    }

    #logo-turtle {
        width: 200px;
        top: 35%;
    }
    #logo-flower-nav {
        width: 85px;
        transform: translate( 385%,-40%);
    
    }

    .title-main {
        max-width: 95%;
    }

    header article {
        width: 95%;
        top: 50%;
    }
    .post-content-pension-see {
        width: 100%;
    }
    .gallery-pension-mistral {
        width: 100%;
    }

    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 90%;
        margin: 5% auto 5% auto;
    }
    article h1 {
        font-size: 4rem;
    }
    article h2 {
        font-size: 2rem;
    }
    article h4 {
        font-size: 1rem;
    }
}


/* ************************************************************************ */
@media only screen and (max-width: 516px) and (orientation: landscape) {
    #calling-phone-number-nav {
        transform: translate(-60%,50%);
    }
    #logo-flower-nav {
        width: 85px;
        transform: translate( 0%,0%);
    }

    .aside-image-frame img {
        width: 95%;
    }
}


/* *********************************************** */
@media only screen and (max-width: 500px){
    #logo_nav_txt {
        font-size: 20px;
        transform: translate(-50%,-35%);
    }

    #calling-phone-number-nav {
        transform: translate(50%,20%);
        font-size: 15px;
        letter-spacing: 0.1px;
    }

    #logo-flower-nav {
        width: 75px;
        transform: translate( 295%,-40%);
    }

    #logo-turtle {
        width:  21rem;
        margin-top: 30px;
    }

/*
    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 90%;
        margin: 5% auto 5% auto;
    }
    article h1 {
        font-size: 3.2rem;
    }
    article h2 {
        font-size: 1.8rem;
    }
    article h4 {
        font-size: 1rem;
    }

    .title-main {
        max-width: 95%;
    }

    header article {
        width: 95%;
        top: 50%;
    }
    .post-content-pension-see {
        width: 95%;
    }
    .gallery-pension-mistral {
        width: 95%;
    }
    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 95%;
        margin: 5% auto;
    }

}
*/


/* ************************************************************************ */
@media only screen and (max-width: 380px){
    #logo_nav_txt {
        font-size: 20px;
        transform: translate(-50%,-32%);
    }

    #calling-phone-number-nav {
        font-size: 12px;
        letter-spacing: 0.1px;
        transform: translate(50%,35%);

    }

    #logo-flower-nav {
        width: 85px;
        transform: translate( 245%,-40%);
    }

    #logo-turtle {
        width:  15rem;
        margin-top: 30px;
    }

    article h1 {
        font-size: 3rem;
    }
    article h2 {
        font-size: 1.8rem;
    }
    article h4 {
        font-size: 1rem;
    }

    .title-main {
        max-width: 95%;
    }

    .title-main {
        max-width: 95%;
    }

    header article {
        width: 95%;
        top: 50%;
    }
    .post-content-pension-see {
        width: 95%;
    }
    .gallery-pension-mistral {
        width: 95%;
    }
    .frame-gallery-pension-mistral {
        width: 100%;
    }
    .title-gallery-p-mistral {
        width: 95%;
        margin: 5% auto;
    }

}

/* ************************************************************************ */










