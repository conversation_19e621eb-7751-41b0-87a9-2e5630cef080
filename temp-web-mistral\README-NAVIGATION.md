# 🌸 PENSION MISTRAL - NAVIGATION DOKUMENTACE

## 📁 **STRUKTURA SOUBORŮ**

### **CSS Soubory (VYČIŠTĚNO):**

- `css/base.css` - **Základn<PERSON> styly** (fonts, reset, utilities, base components)
- `sl_menu/sl_m_style.css` - **Navigation CSS** (vyčištěno)
- `css/contact.css` - **Kontaktní formulář**
- `css/responsive-unified.css` - **Responzivní styly** (všechny breakpointy)
- `css/style.css` - **Specifické styly** (pouze custom hover efekty)

### **JavaScript Soubory:**

- `js/script.js` - **Navigation funkcionalita** (vyčištěno)

### **HTML:**

- `index.html` - **<PERSON>lavn<PERSON> stránka** s vyčištěnou CSS strukturou

---

## 🎯 **FUNKCIONALITA**

### **✅ DESKTOP MENU (1200px+):**

- Horizontální menu viditelné
- Hover efekty s barevným podtržením
- Hamburger skrytý

### **✅ TABLET MENU (769px-1199px):**

- Hamburger ikona vpravo
- Vysouvací menu s animacemi
- Původní hamburger → X animace

### **✅ MOBIL MENU (768px-):**

- Hamburger ikona vpravo
- Vysouvací menu s animacemi
- Auto-close po kliknutí na položku
- Vycentrovaný logo pod 800px

---

## 🔧 **BREAKPOINTY**

```css
/* Desktop */
@media (min-width: 1200px) {
  ...;
}

/* Tablet */
@media (min-width: 769px) and (max-width: 1199px) {
  ...;
}

/* Mobil */
@media (max-width: 768px) {
  ...;
}

/* Logo layout */
@media (max-width: 800px) {
  ...;
}

/* Malé mobily */
@media (max-width: 640px) {
  ...;
}
@media (max-width: 480px) {
  ...;
}
```

---

## 🎮 **SCROLLING MAPOVÁNÍ**

### **Menu položky → Sekce:**

```javascript
'Home' → header nebo .jq--home-top
'Pension Mistral' → .body-section-title.jq--pension-see
'Photos' → .gallery-pension-mistral-grid
'Agios Nikitas' → najde sekci s nadpisem "Agios Nikitas"
'Contact' → .contact-form-maps-pension-mistral
```

---

## 📝 **VYČIŠTĚNÉ ZMĚNY**

### **🧹 KOMPLETNÍ CSS ÚKLID:**

- ✅ **Vytvořen `css/base.css`** - všechny základní styly
- ✅ **Vyčištěn `css/style.css`** - pouze specifické styly (36 řádků z 592)
- ✅ **Reorganizován `css/responsive-unified.css`** - pouze responzivní pravidla
- ✅ **Odstraněny duplicitní soubory** (`css-queries/`, `breakpoints.css`, `grid.css`)
- ✅ **Logická struktura načítání** CSS v HTML

### **📊 CSS STATISTIKY:**

| **Soubor**               | **Před**        | **Po**        | **Účel**          |
| ------------------------ | --------------- | ------------- | ----------------- |
| `style.css`              | 592 řádků       | 36 řádků      | Specifické styly  |
| `base.css`               | -               | 300 řádků     | Základní styly    |
| `responsive-unified.css` | 559 řádků       | 400 řádků     | Responzivní styly |
| **Celkem**               | **1151+ řádků** | **736 řádků** | **36% úspora**    |

### **JavaScript Úklid:**

- ✅ Pouze navigation funkcionalita
- ✅ Odstraněny nepotřebné utility funkce
- ✅ Čistý, čitelný kód
- ✅ Fallback selektory pro všechny sekce

### **Struktura CSS:**

- ✅ **Logické pořadí načítání:** Base → Components → Responsive → Specific
- ✅ **Žádné konflikty** mezi soubory
- ✅ **Jasné komentáře** v češtině

---

## 🚀 **PŘIPRAVENO PRO ÚPRAVY**

### **Přidání nové menu položky:**

1. **HTML:** Přidat `<li><a href="#">Nová položka</a></li>`
2. **JavaScript:** Přidat case do switch statement
3. **CSS:** Automaticky zdědí styling

### **Změna breakpointů:**

- Upravit pouze v `sl_menu/sl_m_style.css`
- Žádné konflikty s ostatními soubory

### **Úprava animací:**

- Hamburger animace v sekci "Hamburger animace"
- Menu animace v responzivních sekcích

---

## ⚠️ **DŮLEŽITÉ POZNÁMKY**

### **Neměnit:**

- HTML strukturu menu (checkbox + label + ul)
- Základní CSS selektory (.menu, .menu-button-container)
- JavaScript event listenery

### **Bezpečné změny:**

- Barvy a fonty
- Animace timing
- Breakpoint hodnoty
- Přidání nových menu položek

---

## 🔄 **ZÁLOHA**

Před úpravami vytvořte zálohu:

```bash
# Záloha celého projektu
robocopy "temp-web-mistral" "temp-web-mistral-backup" /E
```

---

## 📞 **KONTAKT PRO ÚPRAVY**

Kód je nyní čistý, organizovaný a připravený pro další vývoj.
Všechny konflikty odstraněny, funkcionalita zachována.

**Status:** ✅ PŘIPRAVENO PRO PRODUKCI
