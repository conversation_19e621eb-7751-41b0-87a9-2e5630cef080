/* ===== BOOTSTRAP 5 STANDARDIZED BREAKPOINTS ===== */
/* 
Unified breakpoint system based on Bootstrap 5:
- xs: 0px (default, no media query needed)
- sm: 576px (landscape phones)
- md: 768px (tablets)
- lg: 992px (desktops)
- xl: 1200px (large desktops)
- xxl: 1400px (extra large desktops)
*/

/* ===== CSS CUSTOM PROPERTIES FOR BREAKPOINTS ===== */
:root {
  /* Breakpoint values */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-xxl: 1400px;
  
  /* Container max-widths */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
  
  /* Common spacing values */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 3rem;
}

/* ===== UTILITY MIXINS (commented for reference) ===== */
/*
// Min-width media queries (mobile-first)
@mixin media-sm-up { @media (min-width: 576px) { @content; } }
@mixin media-md-up { @media (min-width: 768px) { @content; } }
@mixin media-lg-up { @media (min-width: 992px) { @content; } }
@mixin media-xl-up { @media (min-width: 1200px) { @content; } }
@mixin media-xxl-up { @media (min-width: 1400px) { @content; } }

// Max-width media queries (desktop-first)
@mixin media-xs-down { @media (max-width: 575.98px) { @content; } }
@mixin media-sm-down { @media (max-width: 767.98px) { @content; } }
@mixin media-md-down { @media (max-width: 991.98px) { @content; } }
@mixin media-lg-down { @media (max-width: 1199.98px) { @content; } }
@mixin media-xl-down { @media (max-width: 1399.98px) { @content; } }

// Between breakpoints
@mixin media-sm-only { @media (min-width: 576px) and (max-width: 767.98px) { @content; } }
@mixin media-md-only { @media (min-width: 768px) and (max-width: 991.98px) { @content; } }
@mixin media-lg-only { @media (min-width: 992px) and (max-width: 1199.98px) { @content; } }
@mixin media-xl-only { @media (min-width: 1200px) and (max-width: 1399.98px) { @content; } }
*/

/* ===== RESPONSIVE CONTAINERS ===== */
.container {
  width: 100%;
  padding-right: var(--spacing-md);
  padding-left: var(--spacing-md);
  margin-right: auto;
  margin-left: auto;
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .container {
    max-width: var(--container-sm);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .container {
    max-width: var(--container-lg);
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Extra extra large devices (larger desktops, 1400px and up) */
@media (min-width: 1400px) {
  .container {
    max-width: var(--container-xxl);
  }
}

/* ===== RESPONSIVE UTILITIES ===== */

/* Display utilities */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

/* Responsive display utilities */
@media (max-width: 575.98px) {
  .d-xs-none { display: none !important; }
  .d-xs-block { display: block !important; }
  .d-xs-flex { display: flex !important; }
}

@media (min-width: 576px) {
  .d-sm-none { display: none !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
}

@media (min-width: 1200px) {
  .d-xl-none { display: none !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
}

/* ===== RESPONSIVE SPACING ===== */

/* Margin utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xs) !important; }
.m-2 { margin: var(--spacing-sm) !important; }
.m-3 { margin: var(--spacing-md) !important; }
.m-4 { margin: var(--spacing-lg) !important; }
.m-5 { margin: var(--spacing-xl) !important; }

/* Padding utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* ===== RESPONSIVE TEXT ===== */

/* Font sizes */
.fs-1 { font-size: 2.5rem !important; }
.fs-2 { font-size: 2rem !important; }
.fs-3 { font-size: 1.75rem !important; }
.fs-4 { font-size: 1.5rem !important; }
.fs-5 { font-size: 1.25rem !important; }
.fs-6 { font-size: 1rem !important; }

/* Responsive font sizes */
@media (max-width: 575.98px) {
  .fs-xs-1 { font-size: 2rem !important; }
  .fs-xs-2 { font-size: 1.75rem !important; }
  .fs-xs-3 { font-size: 1.5rem !important; }
  .fs-xs-4 { font-size: 1.25rem !important; }
  .fs-xs-5 { font-size: 1.1rem !important; }
  .fs-xs-6 { font-size: 0.9rem !important; }
}

@media (min-width: 576px) and (max-width: 767.98px) {
  .fs-sm-1 { font-size: 2.2rem !important; }
  .fs-sm-2 { font-size: 1.8rem !important; }
  .fs-sm-3 { font-size: 1.6rem !important; }
  .fs-sm-4 { font-size: 1.3rem !important; }
  .fs-sm-5 { font-size: 1.15rem !important; }
  .fs-sm-6 { font-size: 0.95rem !important; }
}

/* Text alignment */
.text-start { text-align: left !important; }
.text-center { text-align: center !important; }
.text-end { text-align: right !important; }

/* Responsive text alignment */
@media (max-width: 575.98px) {
  .text-xs-start { text-align: left !important; }
  .text-xs-center { text-align: center !important; }
  .text-xs-end { text-align: right !important; }
}

@media (min-width: 576px) {
  .text-sm-start { text-align: left !important; }
  .text-sm-center { text-align: center !important; }
  .text-sm-end { text-align: right !important; }
}

@media (min-width: 768px) {
  .text-md-start { text-align: left !important; }
  .text-md-center { text-align: center !important; }
  .text-md-end { text-align: right !important; }
}
