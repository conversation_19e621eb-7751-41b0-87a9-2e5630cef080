/* ===== UNIFIED RESPONSIVE STYLES FOR PENSION MISTRAL ===== */
/* Using Bootstrap 5 breakpoints: 576px, 768px, 992px, 1200px, 1400px */

/* ===== NAVIGATION RESPONSIVE ===== */

/* Base styles (xs: 0px+) */
.top-nav {
  padding: 1em 40px;
}

#logo_nav_txt {
  font-size: 45px;
}

#calling-phone-number-nav {
  font-size: 25px;
}

#logo-flower-nav {
  width: 100px;
}

/* Small devices (sm: 576px+) */
@media (min-width: 576px) {
  .top-nav {
    padding: 1em 35px;
  }
  
  #logo_nav_txt {
    font-size: 42px;
  }
  
  #calling-phone-number-nav {
    font-size: 22px;
  }
  
  #logo-flower-nav {
    width: 90px;
  }
}

/* Medium devices (md: 768px+) - POZOR: Hamburger menu je ř<PERSON>zeno z sl_menu/sl_m_style.css */
@media (min-width: 768px) {
  /* Neřídíme hamburger menu zde - je řízeno z sl_menu/sl_m_style.css */
  /* .menu-button-container { display: none; } */
  /* .menu { display: flex !important; } */
}

/* Max-width queries for mobile-first approach */

/* Extra small devices (max-width: 575.98px) */
@media (max-width: 575.98px) {
  .top-nav {
    padding: 1em 20px;
  }
  
  .logo-block {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
  }
  
  #logo_nav_txt {
    font-size: 28px;
  }
  
  #calling-phone-number-nav {
    position: relative;
    top: auto;
    left: auto;
    font-size: 12px;
    margin-top: 2px;
  }
  
  #logo-flower-nav {
    width: 50px;
    margin-left: 8px;
    margin-top: -8px;
  }
  
  /* Menu řízeno z sl_menu/sl_m_style.css */
  /* .menu-button-container { display: flex; margin-left: auto; } */
  /* .menu { display: none; } */
}

/* Small devices only (576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .logo-block {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
  }
  
  #logo_nav_txt {
    font-size: 33px;
  }
  
  #calling-phone-number-nav {
    position: relative;
    top: auto;
    left: auto;
    font-size: 14px;
    margin-top: 2px;
  }
  
  #logo-flower-nav {
    width: 60px;
    margin-left: 10px;
    margin-top: -10px;
  }
  
  /* Menu řízeno z sl_menu/sl_m_style.css */
  /* .menu-button-container { display: flex; margin-left: auto; } */
  /* .menu { display: none; } */
}

/* ===== HERO SECTION RESPONSIVE ===== */

/* Base styles */
header {
  height: 100vh;
  background-attachment: fixed;
  padding-top: 100px;
}

#logo-turtle {
  max-width: 25rem;
  top: 25%;
}

article h1 {
  font-size: 7rem;
}

article h2 {
  font-size: 4rem;
}

article h4 {
  font-size: 1.8rem;
}

/* Large devices (lg: 992px+) */
@media (min-width: 992px) {
  header {
    height: 100vh;
    background-attachment: fixed;
  }
  
  #logo-turtle {
    max-width: 25rem;
    top: 25%;
  }
  
  article h1 {
    font-size: 7rem;
  }
  
  article h2 {
    font-size: 4rem;
  }
  
  article h4 {
    font-size: 1.8rem;
  }
}

/* Medium devices (md: 768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  header {
    height: 90vh;
    background-attachment: scroll;
  }
  
  #logo-turtle {
    max-width: 20rem;
    top: 22%;
  }
  
  article h1 {
    font-size: 5rem;
  }
  
  article h2 {
    font-size: 3rem;
  }
  
  article h4 {
    font-size: 1.5rem;
  }
}

/* Small devices (sm: 576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  header {
    height: 100vh;
    background-attachment: scroll;
    min-height: 600px;
  }
  
  #logo-turtle {
    max-width: 18rem;
    top: 20%;
  }
  
  header article {
    top: 45%;
    width: 90%;
  }
  
  article h1 {
    font-size: 4rem;
    margin-top: 1rem;
  }
  
  article h2 {
    font-size: 2.5rem;
  }
  
  article h4 {
    font-size: 1.2rem;
    margin-top: 1rem;
    line-height: 1.4;
  }
}

/* Extra small devices (max-width: 575.98px) */
@media (max-width: 575.98px) {
  header {
    height: 100vh;
    min-height: 500px;
    background-attachment: scroll;
  }
  
  #logo-turtle {
    max-width: 15rem;
    top: 18%;
  }
  
  header article {
    top: 42%;
    width: 95%;
  }
  
  article h1 {
    font-size: 2.5rem;
    margin-top: 0.5rem;
  }
  
  article h2 {
    font-size: 1.8rem;
    margin-top: 0.5rem;
  }
  
  article h4 {
    font-size: 1rem;
    margin-top: 0.8rem;
    line-height: 1.3;
    padding: 0 1rem;
  }
}

/* ===== CONTENT SECTIONS RESPONSIVE ===== */

/* Base styles */
.title-main h3 {
  font-size: 3rem;
  padding: 0 1rem;
}

.post-content-pension-see {
  padding: 0 2rem;
}

.pension-mistral-p p {
  font-size: 1rem;
  line-height: 1.6;
}

/* Large devices (lg: 992px+) */
@media (min-width: 992px) {
  .title-main h3 {
    font-size: 3.5rem;
  }
  
  .pension-mistral-p p {
    font-size: 1.1rem;
  }
}

/* Medium devices (md: 768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .title-main h3 {
    font-size: 2.8rem;
  }
  
  .post-content-pension-see {
    padding: 0 1.5rem;
  }
}

/* Small devices (sm: 576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .title-main h3 {
    font-size: 2.2rem;
    padding: 0 0.8rem;
  }
  
  .post-content-pension-see {
    padding: 0 1rem;
  }
  
  .pension-mistral-p p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Extra small devices (max-width: 575.98px) */
@media (max-width: 575.98px) {
  .title-main h3 {
    font-size: 1.8rem;
    padding: 0 0.5rem;
  }
  
  .post-content-pension-see {
    padding: 0 0.8rem;
  }
  
  .pension-mistral-p p {
    font-size: 0.9rem;
    line-height: 1.4;
    text-align: left;
  }
}

/* ===== GALLERY RESPONSIVE ===== */

/* Base styles */
.gallery-pension-mistral-grid {
  padding: 20px;
}

.gallery-pension-mistral-grid .cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Extra large devices (xxl: 1400px+) */
@media (min-width: 1400px) {
  .gallery-pension-mistral-grid .cards {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2.5rem;
    max-width: 1400px;
  }
}

/* Large devices (lg: 992px - 1199.98px) */
@media (min-width: 992px) and (max-width: 1199.98px) {
  .gallery-pension-mistral-grid .cards {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.8rem;
  }
}

/* Medium devices (md: 768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .gallery-pension-mistral-grid {
    padding: 15px;
  }

  .gallery-pension-mistral-grid .cards {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }

  .title-main h3 {
    font-size: 2.5rem;
  }
}

/* Small devices (sm: 576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .gallery-pension-mistral-grid {
    padding: 12px;
  }

  .gallery-pension-mistral-grid .cards {
    grid-template-columns: 1fr;
    gap: 1.2rem;
  }

  .title-main h3 {
    font-size: 2rem;
    text-align: center;
  }
}

/* Extra small devices (max-width: 575.98px) */
@media (max-width: 575.98px) {
  .gallery-pension-mistral-grid {
    padding: 10px;
  }

  .gallery-pension-mistral-grid .cards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .title-main h3 {
    font-size: 1.8rem;
    text-align: center;
  }

  .title-gallery-p-mistral {
    margin: 2% 5%;
    font-size: 1rem;
    padding: 8px;
  }
}

/* ===== CONTACT FORM RESPONSIVE ===== */

/* Base styles */
.contact-form-text-h1,
.contact-left-side h1 {
  font-size: 4em;
}

.frame-map {
  width: 500px;
  height: 300px;
}

form {
  width: 500px;
  max-width: 100%;
}

/* Large devices (lg: 992px+) */
@media (min-width: 992px) {
  .cards {
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2rem;
    padding: 0 20px;
  }
}

/* Medium devices (md: 768px - 991.98px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .cards {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 20px;
  }

  .contact-form-text-h1,
  .contact-left-side h1 {
    font-size: 3em;
  }

  .frame-map {
    width: 100%;
    max-width: 400px;
    height: 250px;
  }
}

/* Small devices (sm: 576px - 767.98px) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .contact-form-text-h1,
  .contact-left-side h1 {
    font-size: 2.5em;
    padding: 20px 0 10px 0;
  }

  #hero-forms {
    height: auto;
    padding: 40px 0;
  }

  form {
    width: 100%;
    padding: 0 20px;
  }

  .contact-row {
    flex-direction: column;
  }

  .contact-row .input-group {
    flex-basis: 100%;
    margin-bottom: 20px;
  }

  input, textarea {
    width: 100%;
    font-size: 16px;
  }

  button {
    width: 100%;
    padding: 15px 0;
    font-size: 16px;
  }

  .frame-map {
    width: 100%;
    height: 200px;
  }
}

/* Extra small devices (max-width: 575.98px) */
@media (max-width: 575.98px) {
  .cards {
    padding: 0 10px;
    gap: 1rem;
  }

  .contact-form-text-h1,
  .contact-left-side h1 {
    font-size: 2em;
    padding: 15px 0 8px 0;
  }

  .contact-left-side p {
    font-size: 14px;
    padding: 3px;
  }

  #hero-forms {
    padding: 20px 0;
  }

  form {
    padding: 0 10px;
  }

  .input-group {
    margin-bottom: 25px;
  }

  input, textarea {
    padding: 12px 8px;
    font-size: 16px;
  }

  label {
    padding: 12px 8px;
    font-size: 14px;
  }

  button {
    padding: 12px 0;
    font-size: 14px;
  }

  .frame-map {
    height: 180px;
  }
}
