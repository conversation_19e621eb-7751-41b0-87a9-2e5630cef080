// ===== PENSION MISTRAL - FUNKČNÍ SCROLLING =====

document.addEventListener('DOMContentLoaded', function () {
    console.log('🌸 JavaScript načten!');

    // ===== MENU SCROLLING FUNKCIONALITA =====
    const menuItems = document.querySelectorAll('.menu li a');
    const menuToggle = document.getElementById('menu-toggle');
    const menuButton = document.querySelector('.menu-button-container');

    console.log('Menu items found:', menuItems.length);
    console.log('Menu toggle found:', !!menuToggle);
    console.log('Menu button found:', !!menuButton);

    // Funkce pro zavření menu
    function closeMenu() {
        if (menuToggle) {
            menuToggle.checked = false;
            console.log('Menu zavřeno');
        }
    }

    // Test hamburger menu
    if (menuButton) {
        menuButton.addEventListener('click', function () {
            console.log('Hamburger kliknuto!');
            console.log('Menu toggle checked:', menuToggle ? menuToggle.checked : 'N/A');
        });
    }

    // Funkce pro smooth scroll
    function smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            const offsetTop = element.offsetTop - 120; // Offset pro fixed header
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            return true;
        }
        return false;
    }

    // Přidej event listenery na menu položky
    menuItems.forEach(function (item, index) {
        console.log(`Menu item ${index}:`, item.textContent.trim());

        // Přidej click i touch eventy pro mobilní zařízení
        function handleMenuClick(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('🔥 KLIK/TOUCH DETEKOVÁN!');

            const text = this.textContent.trim();
            console.log('Kliknuto na:', text);
            console.log('Element:', this);
            console.log('Event type:', e.type);

            let scrolled = false;

            // Zkus najít sekci podle textu
            switch (text) {
                case 'Home':
                    scrolled = smoothScrollTo('header') || smoothScrollTo('.jq--home-top');
                    break;

                case 'Pension Mistral':
                    scrolled = smoothScrollTo('.body-section-title.jq--pension-see') ||
                        smoothScrollTo('.body-section-title') ||
                        smoothScrollTo('.jq--pension-see');
                    break;

                case 'Photos':
                    scrolled = smoothScrollTo('.gallery-pension-mistral-grid') ||
                        smoothScrollTo('.jq--photos-see');
                    break;

                case 'Agios Nikitas':
                    // Najdi sekci s "Agios Nikitas" nadpisem
                    const agiosHeaders = document.querySelectorAll('.title-main h3');
                    for (let h3 of agiosHeaders) {
                        if (h3.textContent.includes('Agios Nikitas')) {
                            const section = h3.closest('section');
                            if (section) {
                                const offsetTop = section.offsetTop - 120;
                                window.scrollTo({ top: offsetTop, behavior: 'smooth' });
                                scrolled = true;
                                break;
                            }
                        }
                    }
                    if (!scrolled) {
                        scrolled = smoothScrollTo('.jq--agnikitas-see');
                    }
                    break;

                case 'Contact':
                    scrolled = smoothScrollTo('.contact-form-maps-pension-mistral') ||
                        smoothScrollTo('.jq--contact');
                    break;
            }

            if (!scrolled) {
                console.warn('Sekce nenalezena pro:', text);
            }

            // Zavři menu po kliknutí (pouze na mobilech)
            if (window.innerWidth < 1200) {
                setTimeout(closeMenu, 300);
            }
        }

        // Přidej event listenery pro click i touch
        item.addEventListener('click', handleMenuClick);
        item.addEventListener('touchend', handleMenuClick);

        // Přidej také pointer events pro lepší kompatibilitu
        if ('PointerEvent' in window) {
            item.addEventListener('pointerup', handleMenuClick);
        }
    });

    // ===== CONSOLE LOG =====
    console.log('🌸 Pension Mistral - Navigation loaded successfully!');

});
