// ===== PENSION MISTRAL - FUNKČNÍ SCROLLING =====

document.addEventListener('DOMContentLoaded', function () {

    // ===== MENU SCROLLING FUNKCIONALITA =====
    const menuItems = document.querySelectorAll('.menu li a');
    const menuToggle = document.getElementById('menu-toggle');

    // Funkce pro zavření menu
    function closeMenu() {
        if (menuToggle) {
            menuToggle.checked = false;
        }
    }

    // Funkce pro smooth scroll
    function smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            const offsetTop = element.offsetTop - 120; // Offset pro fixed header
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            return true;
        }
        return false;
    }

    // Přidej event listenery na menu položky
    menuItems.forEach(function (item) {
        item.addEventListener('click', function (e) {
            e.preventDefault();

            const text = this.textContent.trim();
            console.log('Kliknuto na:', text);

            let scrolled = false;

            // Zkus najít sekci podle textu
            switch (text) {
                case 'Home':
                    scrolled = smoothScrollTo('header') || smoothScrollTo('.jq--home-top');
                    break;

                case 'Pension Mistral':
                    scrolled = smoothScrollTo('.body-section-title.jq--pension-see') ||
                        smoothScrollTo('.body-section-title') ||
                        smoothScrollTo('.jq--pension-see');
                    break;

                case 'Photos':
                    scrolled = smoothScrollTo('.gallery-pension-mistral-grid') ||
                        smoothScrollTo('.jq--photos-see');
                    break;

                case 'Agios Nikitas':
                    // Najdi sekci s "Agios Nikitas" nadpisem
                    const agiosHeaders = document.querySelectorAll('.title-main h3');
                    for (let h3 of agiosHeaders) {
                        if (h3.textContent.includes('Agios Nikitas')) {
                            const section = h3.closest('section');
                            if (section) {
                                const offsetTop = section.offsetTop - 120;
                                window.scrollTo({ top: offsetTop, behavior: 'smooth' });
                                scrolled = true;
                                break;
                            }
                        }
                    }
                    if (!scrolled) {
                        scrolled = smoothScrollTo('.jq--agnikitas-see');
                    }
                    break;

                case 'Contact':
                    scrolled = smoothScrollTo('.contact-form-maps-pension-mistral') ||
                        smoothScrollTo('.jq--contact');
                    break;
            }

            if (!scrolled) {
                console.warn('Sekce nenalezena pro:', text);
            }

            // Zavři menu po kliknutí (pouze na mobilech)
            if (window.innerWidth < 1200) {
                setTimeout(closeMenu, 300);
            }
        });
    });


    // ===== CONSOLE LOG =====
    console.log('🌸 Pension Mistral - Navigation loaded successfully!');

});
