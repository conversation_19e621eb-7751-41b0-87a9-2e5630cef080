// ===== PENSION MISTRAL - FUNKČNÍ SCROLLING =====

document.addEventListener('DOMContentLoaded', function () {

    // ===== MENU SCROLLING FUNKCIONALITA =====
    const menuItems = document.querySelectorAll('.menu li a');
    const menuToggle = document.getElementById('menu-toggle');

    // Funkce pro zavření menu
    function closeMenu() {
        if (menuToggle) {
            menuToggle.checked = false;
        }
    }

    // Funkce pro smooth scroll
    function smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            const offsetTop = element.offsetTop - 120; // Offset pro fixed header
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
            return true;
        }
        return false;
    }

    // Přidej event listenery na menu položky
    menuItems.forEach(function (item) {
        item.addEventListener('click', function (e) {
            e.preventDefault();

            const text = this.textContent.trim();
            console.log('Kliknuto na:', text);

            let scrolled = false;

            // Zkus najít sekci podle textu
            switch (text) {
                case 'Home':
                    scrolled = smoothScrollTo('header') || smoothScrollTo('.jq--home-top');
                    break;

                case 'Pension Mistral':
                    scrolled = smoothScrollTo('.body-section-title.jq--pension-see') ||
                        smoothScrollTo('.body-section-title') ||
                        smoothScrollTo('.jq--pension-see');
                    break;

                case 'Photos':
                    scrolled = smoothScrollTo('.gallery-pension-mistral-grid') ||
                        smoothScrollTo('.jq--photos-see');
                    break;

                case 'Agios Nikitas':
                    // Najdi sekci s "Agios Nikitas" nadpisem
                    const agiosHeaders = document.querySelectorAll('.title-main h3');
                    for (let h3 of agiosHeaders) {
                        if (h3.textContent.includes('Agios Nikitas')) {
                            const section = h3.closest('section');
                            if (section) {
                                const offsetTop = section.offsetTop - 120;
                                window.scrollTo({ top: offsetTop, behavior: 'smooth' });
                                scrolled = true;
                                break;
                            }
                        }
                    }
                    if (!scrolled) {
                        scrolled = smoothScrollTo('.jq--agnikitas-see');
                    }
                    break;

                case 'Contact':
                    scrolled = smoothScrollTo('.contact-form-maps-pension-mistral') ||
                        smoothScrollTo('.jq--contact');
                    break;
            }

            if (!scrolled) {
                console.warn('Sekce nenalezena pro:', text);
            }

            // Zavři menu po kliknutí (pouze na mobilech)
            if (window.innerWidth < 1200) {
                setTimeout(closeMenu, 300);
            }
        });
    });


    // ===== SMOOTH SCROLLING FOR ANCHOR LINKS =====
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach(function (link) {
        link.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // Pokud je to pouze #, neprovádět smooth scroll
            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();

                // Zavři menu pokud je otevřené
                closeMenu();

                // Smooth scroll k cíli
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });


    // ===== RESPONSIVE NAVIGATION HEIGHT ADJUSTMENT =====
    function adjustNavigationHeight() {
        const nav = document.querySelector('.top-nav');
        if (nav) {
            const navHeight = nav.offsetHeight;
            document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
        }
    }

    // Nastav výšku navigace při načtení a změně velikosti okna
    adjustNavigationHeight();
    window.addEventListener('resize', adjustNavigationHeight);


    // ===== LAZY LOADING FOR IMAGES =====
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function (entries, observer) {
            entries.forEach(function (entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(function (img) {
            imageObserver.observe(img);
        });
    }


    // ===== FORM VALIDATION ENHANCEMENT =====
    const forms = document.querySelectorAll('form');

    forms.forEach(function (form) {
        form.addEventListener('submit', function (e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(function (field) {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Prosím vyplňte všechna povinná pole.');
            }
        });
    });


    // ===== CONSOLE LOG FOR DEBUGGING =====
    console.log('🌸 Pension Mistral - JavaScript loaded successfully!');
    console.log('📱 Mobile menu auto-close: ✅');
    console.log('🔗 Smooth scrolling: ✅');
    console.log('📐 Responsive navigation: ✅');

});

// ===== UTILITY FUNCTIONS =====

// Funkce pro detekci mobilního zařízení
function isMobile() {
    return window.innerWidth <= 768;
}

// Funkce pro detekci touch zařízení
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// Funkce pro throttling event handlerů
function throttle(func, limit) {
    let inThrottle;
    return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
