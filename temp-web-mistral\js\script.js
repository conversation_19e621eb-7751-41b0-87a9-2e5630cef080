// ===== PENSION MISTRAL - NOVÝ FUNKČNÍ JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function () {

    // ===== DEFINICE SEKCÍ PRO SCROLLING =====
    const sections = {
        'Home': 'header',
        'Pension Mistral': '#pension-mistral-section',
        'Photos': '#gallery-section',
        'Agios Nikitas': '#agios-nikitas-section',
        'Contact': '#contact-section'
    };

    // ===== MENU FUNKCIONALITA =====
    const menuToggle = document.getElementById('menu-toggle');
    const menuItems = document.querySelectorAll('.menu li a');
    const menu = document.querySelector('.menu');
    const menuButton = document.querySelector('.menu-button-container');

    // Funkce pro zavření menu
    function closeMenu() {
        if (menuToggle) {
            menuToggle.checked = false;
        }
    }

    // Funkce pro smooth scroll
    function smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            const offsetTop = element.offsetTop - 100; // Offset pro fixed header
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    }

    // Přidej event listenery na menu položky
    menuItems.forEach(function (item, index) {
        item.addEventListener('click', function (e) {
            e.preventDefault();

            const text = this.textContent.trim();
            console.log('Kliknuto na:', text);

            // Najdi odpovídající sekci
            let targetSection = null;

            switch (text) {
                case 'Home':
                    targetSection = 'header';
                    break;
                case 'Pension Mistral':
                    targetSection = '#pension-mistral-section';
                    break;
                case 'Photos':
                    targetSection = '#gallery-section';
                    break;
                case 'Agios Nikitas':
                    targetSection = '#agios-nikitas-section';
                    break;
                case 'Contact':
                    targetSection = '#contact-section';
                    break;
            }

            // Pokud sekce neexistuje, zkus skutečné selektory z HTML
            if (targetSection && !document.querySelector(targetSection)) {
                switch (text) {
                    case 'Pension Mistral':
                        targetSection = '.body-section-title.jq--pension-see';
                        break;
                    case 'Photos':
                        targetSection = '.gallery-pension-mistral-grid';
                        break;
                    case 'Agios Nikitas':
                        // Najdi sekci s "Agios Nikitas" nadpisem
                        const agiosSection = Array.from(document.querySelectorAll('.title-main h3'))
                            .find(h3 => h3.textContent.includes('Agios Nikitas'));
                        if (agiosSection) {
                            targetSection = agiosSection.closest('section');
                        }
                        break;
                    case 'Contact':
                        targetSection = '.contact-form-maps-pension-mistral';
                        break;
                }
            }

            // Scroll k sekci
            if (targetSection) {
                smoothScrollTo(targetSection);
            } else {
                console.warn('Sekce nenalezena pro:', text);
            }

            // Zavři menu po kliknutí
            setTimeout(closeMenu, 300);
        });
    });

    // Zavři menu při kliknutí mimo menu
    document.addEventListener('click', function (event) {
        if (menuToggle && menuToggle.checked) {
            if (!menu.contains(event.target) && !menuButton.contains(event.target)) {
                closeMenu();
            }
        }
    });

    // Zavři menu při stisknutí ESC
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape' && menuToggle && menuToggle.checked) {
            closeMenu();
        }
    });


    // ===== SMOOTH SCROLLING FOR ANCHOR LINKS =====
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach(function (link) {
        link.addEventListener('click', function (e) {
            const href = this.getAttribute('href');

            // Pokud je to pouze #, neprovádět smooth scroll
            if (href === '#') return;

            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();

                // Zavři menu pokud je otevřené
                closeMenu();

                // Smooth scroll k cíli
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });


    // ===== RESPONSIVE NAVIGATION HEIGHT ADJUSTMENT =====
    function adjustNavigationHeight() {
        const nav = document.querySelector('.top-nav');
        if (nav) {
            const navHeight = nav.offsetHeight;
            document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
        }
    }

    // Nastav výšku navigace při načtení a změně velikosti okna
    adjustNavigationHeight();
    window.addEventListener('resize', adjustNavigationHeight);


    // ===== LAZY LOADING FOR IMAGES =====
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function (entries, observer) {
            entries.forEach(function (entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(function (img) {
            imageObserver.observe(img);
        });
    }


    // ===== FORM VALIDATION ENHANCEMENT =====
    const forms = document.querySelectorAll('form');

    forms.forEach(function (form) {
        form.addEventListener('submit', function (e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(function (field) {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Prosím vyplňte všechna povinná pole.');
            }
        });
    });


    // ===== CONSOLE LOG FOR DEBUGGING =====
    console.log('🌸 Pension Mistral - JavaScript loaded successfully!');
    console.log('📱 Mobile menu auto-close: ✅');
    console.log('🔗 Smooth scrolling: ✅');
    console.log('📐 Responsive navigation: ✅');

});

// ===== UTILITY FUNCTIONS =====

// Funkce pro detekci mobilního zařízení
function isMobile() {
    return window.innerWidth <= 768;
}

// Funkce pro detekci touch zařízení
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// Funkce pro throttling event handlerů
function throttle(func, limit) {
    let inThrottle;
    return function () {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
