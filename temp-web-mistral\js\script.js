// ===== PENSION MISTRAL - MAIN JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== AUTO-CLOSE MOBILE MENU AFTER CLICK =====
    const menuToggle = document.getElementById('menu-toggle');
    const menuItems = document.querySelectorAll('.menu li a');
    
    // Funkce pro zavření menu
    function closeMenu() {
        if (menuToggle) {
            menuToggle.checked = false;
        }
    }
    
    // Přidej event listener na každou položku menu
    menuItems.forEach(function(item) {
        item.addEventListener('click', function() {
            // Zavři menu po kliknutí na položku
            setTimeout(closeMenu, 100); // Malé zpoždění pro lepší UX
        });
    });
    
    // Zavři menu při kliknutí mimo menu
    document.addEventListener('click', function(event) {
        const menu = document.querySelector('.menu');
        const menuButton = document.querySelector('.menu-button-container');
        
        // Pokud je menu otevřené a klik není na menu ani na hamburger
        if (menuToggle && menuToggle.checked) {
            if (!menu.contains(event.target) && !menuButton.contains(event.target)) {
                closeMenu();
            }
        }
    });
    
    // Zavři menu při stisknutí ESC
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && menuToggle && menuToggle.checked) {
            closeMenu();
        }
    });
    
    
    // ===== SMOOTH SCROLLING FOR ANCHOR LINKS =====
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    
    anchorLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Pokud je to pouze #, neprovádět smooth scroll
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                
                // Zavři menu pokud je otevřené
                closeMenu();
                
                // Smooth scroll k cíli
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    
    // ===== RESPONSIVE NAVIGATION HEIGHT ADJUSTMENT =====
    function adjustNavigationHeight() {
        const nav = document.querySelector('.top-nav');
        if (nav) {
            const navHeight = nav.offsetHeight;
            document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
        }
    }
    
    // Nastav výšku navigace při načtení a změně velikosti okna
    adjustNavigationHeight();
    window.addEventListener('resize', adjustNavigationHeight);
    
    
    // ===== LAZY LOADING FOR IMAGES =====
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                }
            });
        });
        
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(function(img) {
            imageObserver.observe(img);
        });
    }
    
    
    // ===== FORM VALIDATION ENHANCEMENT =====
    const forms = document.querySelectorAll('form');
    
    forms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(function(field) {
                if (!field.value.trim()) {
                    field.classList.add('error');
                    isValid = false;
                } else {
                    field.classList.remove('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                alert('Prosím vyplňte všechna povinná pole.');
            }
        });
    });
    
    
    // ===== CONSOLE LOG FOR DEBUGGING =====
    console.log('🌸 Pension Mistral - JavaScript loaded successfully!');
    console.log('📱 Mobile menu auto-close: ✅');
    console.log('🔗 Smooth scrolling: ✅');
    console.log('📐 Responsive navigation: ✅');
    
});

// ===== UTILITY FUNCTIONS =====

// Funkce pro detekci mobilního zařízení
function isMobile() {
    return window.innerWidth <= 768;
}

// Funkce pro detekci touch zařízení
function isTouchDevice() {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}

// Funkce pro throttling event handlerů
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
