/* ===== NOVÉ FUNKČNÍ NAVIGATION MENU ===== */

/* Reset a základní styly */
* {
  box-sizing: border-box;
}

/* Hlavní navigace */
.top-nav {
  background: linear-gradient(135deg, #004279 0%, #0066cc 100%);
  padding: 1em 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* <PERSON><PERSON> strana - logo blok */
.nav-left-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.logo-block {
  display: flex;
  align-items: center;
  position: relative;
}

#logo_nav_txt {
  font-family: "Send Flowers", cursive;
  font-size: 45px;
  color: #fff;
  text-shadow: 2px 2px 10px rgba(255, 255, 255, 0.3);
  margin: 0;
  white-space: nowrap;
}

#logo-flower-nav {
  width: 100px;
  margin-left: 15px;
  flex-shrink: 0;
}

#calling-phone-number-nav {
  position: absolute;
  top: 65%;
  left: 0;
  font-family: "Roboto Flex", sans-serif;
  font-size: 25px;
  color: mediumturquoise;
  text-shadow: 2px 2px 10px rgba(255, 255, 255, 0.3);
  letter-spacing: 1.5px;
  margin: 0;
  white-space: nowrap;
}

#calling-phone-number-nav i {
  margin-right: 8px;
}

/* Pravá strana - menu */
.nav-right-content {
  display: flex;
  align-items: center;
}

/* Desktop menu */
.menu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0;
}

.menu li {
  margin: 0;
}

.menu li a {
  display: block;
  padding: 1em 1.5em;
  color: #fff;
  text-decoration: none;
  font-family: "Roboto Flex", sans-serif;
  font-size: 18px;
  font-weight: 500;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.menu li a:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-bottom-color: mediumturquoise;
  transform: translateY(-2px);
}

/* Hamburger menu container */
.menu-button-container {
  display: none;
  width: 30px;
  height: 30px;
  cursor: pointer;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 1001;
}

/* Hamburger checkbox (skrytý) */
#menu-toggle {
  display: none;
}

/* Hamburger ikona */
.menu-button {
  width: 25px;
  height: 3px;
  background-color: #fff;
  border-radius: 2px;
  position: relative;
  transition: all 0.3s ease;
}

.menu-button::before,
.menu-button::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 3px;
  background-color: #fff;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.menu-button::before {
  top: -8px;
}

.menu-button::after {
  top: 8px;
}

/* Hamburger animace při otevření */
#menu-toggle:checked + .menu-button-container .menu-button {
  background-color: transparent;
}

#menu-toggle:checked + .menu-button-container .menu-button::before {
  top: 0;
  transform: rotate(45deg);
}

#menu-toggle:checked + .menu-button-container .menu-button::after {
  top: 0;
  transform: rotate(-45deg);
}

/* ===== RESPONZIVNÍ BREAKPOINTY ===== */

/* Desktop - 1200px a více */
@media (min-width: 1200px) {
  .menu-button-container {
    display: none;
  }
  
  .menu {
    display: flex;
  }
}

/* Tablet a mobil - pod 1200px */
@media (max-width: 1199px) {
  .menu-button-container {
    display: flex;
  }
  
  .menu {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, rgba(0, 66, 121, 0.98) 0%, rgba(0, 102, 204, 0.98) 100%);
    backdrop-filter: blur(10px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }
  
  #menu-toggle:checked ~ .menu {
    display: flex;
    opacity: 1;
    visibility: visible;
  }
  
  .menu li {
    margin: 10px 0;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
  }
  
  #menu-toggle:checked ~ .menu li {
    opacity: 1;
    transform: translateY(0);
  }
  
  /* Postupné animace položek */
  #menu-toggle:checked ~ .menu li:nth-child(1) { transition-delay: 0.1s; }
  #menu-toggle:checked ~ .menu li:nth-child(2) { transition-delay: 0.2s; }
  #menu-toggle:checked ~ .menu li:nth-child(3) { transition-delay: 0.3s; }
  #menu-toggle:checked ~ .menu li:nth-child(4) { transition-delay: 0.4s; }
  #menu-toggle:checked ~ .menu li:nth-child(5) { transition-delay: 0.5s; }
  
  .menu li a {
    font-size: 24px;
    padding: 15px 30px;
    border-radius: 10px;
    border: 2px solid transparent;
    text-align: center;
    min-width: 200px;
  }
  
  .menu li a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: mediumturquoise;
    transform: scale(1.05);
  }
}

/* Pod 800px - vycentrovaný logo blok */
@media (max-width: 800px) {
  .nav-left-content {
    justify-content: center;
  }
  
  .logo-block {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto auto;
    justify-content: center;
    align-items: center;
    gap: 5px 8px;
    text-align: left;
  }
  
  #logo_nav_txt {
    font-size: 32px;
    grid-column: 1;
    grid-row: 1;
  }
  
  #logo-flower-nav {
    width: 60px;
    grid-column: 2;
    grid-row: 1 / 3;
    align-self: center;
    justify-self: center;
    margin-left: 0;
  }
  
  #calling-phone-number-nav {
    position: relative;
    top: auto;
    left: auto;
    font-size: 14px;
    grid-column: 1;
    grid-row: 2;
  }
}

/* Malé mobily */
@media (max-width: 480px) {
  .top-nav {
    padding: 1em 20px;
  }
  
  #logo_nav_txt {
    font-size: 24px;
  }
  
  #calling-phone-number-nav {
    font-size: 12px;
  }
  
  #logo-flower-nav {
    width: 45px;
  }
  
  .menu li a {
    font-size: 20px;
    padding: 12px 25px;
  }
}
