.contact-form-text-h1 {
  font-family: "Send Flowers"; 
  text-align: center;
  color: #004279;
  text-shadow: 2px 2px 5px rgb(96, 96, 96);
  padding: 5px 0 15px 0;
  font-size: 4em;
  }

  .card {
  background-color: rgb(255, 255, 255);
  color: white;
  padding: 1rem;
  height: auto;
  }

  .cards {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
.contact-left-side {
  max-width: 100%;
  max-height: 100%;
}
.contact-left-side h1 {
  font-family: "Send Flowers"; 
  text-align: center;
  color: #004279;
  text-shadow: 2px 2px 5px rgb(96, 96, 96);
  padding: 40px 0 2px 0;
  font-size: 4em;
  }

.contact-left-side p {
  padding:  5px;
  text-decoration: none;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
  text-shadow: 2px 2px 5px rgb(96, 96, 96);
  color: #004279;
  /* border: 1px solid red; */
  }


/* ----  Forms - CARD TWO GRID ----  */
.form-mail {
  background: linear-gradient(rgba(255, 255, 255, 0.9),rgba(255, 255, 255, 0.9)), url("../img/background/contact-form-bg-pm_1980x600px.jpg");
  background-size: cover;
  height: 100%;
  background-attachment: fixed;  
  }


#hero-forms {
  font-family: 'poppins', sans-serif;
  width: 100%;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  }

form {
  width: 95%;
  max-width: 500px;
  }

.input-group {
  margin-bottom: 35px;
  position: relative;
  }

#name-email-group {
  width: 97%;
}

input, textarea {
  width: 95%;
  padding: 10px;
  outline: 0;
  border: 1px solid #004279;
  color: #004279;
  background: transparent;
  font-size: 15px;
  }
  
label {
  height: 95%;
  position: absolute;
  left: 0;
  top: 0;
  padding: 10px;
  color: #004279;
  cursor: text;
  transition: 0.2s;
  }
  
button{
  padding: 10px 0;
  color: #004279;
  outline: none;
  background: transparent;
  border: 1px solid #004279;
  width: 95%;
  cursor: pointer;
  }



#hero-forms input:focus~label,
#hero-forms input:valid~label,
textarea:focus~label,
textarea:valid~label{
  top: -35px;
  font-size: 14px;
  }
  /*
input:focus~label,
input:valid~label,
textarea:focus~label,
textarea:valid~label{
  top: -35px;
  font-size: 14px;
  }*/
  



.contact-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  }
  
.contact-row .input-group {
  flex-basis: 48%;        
  }

form .success {
  text-align: center;
  background-color: #007d81;
  color: #ffffff;
  border-radius: 3px;
  padding: 11px;
  position: relative;
  bottom: 30px;
  }
  
form .error {
  text-align: center;
  color: #ffffff;
  background-color: #72040d;
  border-radius: 3px;
  padding: 11px;
  position: relative;
  bottom: 30px;
  }

.google-maps-frame {
  display: flex;
  justify-content: center;
  border: 1px soli #004279;
  }

.frame-map {
  width: 300px;
  height: 200px;
  }

.success {
    color: #ffffff;
    background-color: #007d81;
    border-radius: 3px;
    }

.error {
    color: #ffffff;
    background-color: #72040d;
    }













/*
body, html {
    margin: 0;
    padding: 0;
    box-sizing: border-box;

}

body {
  font-family: sans-serif;
  background:#fff;

}

.box {
    width: 370px;
    padding: 15px;
    position: relative;
    top: 0%;
    left: 50%;
    transform: translate(-50%, -0%);
    background: #ffffff;
    text-align: center;
    border: 1px solid darkcyan;
    border-radius: 24px;

}
.box h1 {    
    top: 2%;
    left: 50%;
    transform: translate(-2%, 10%);
    color: #ffffff;
    text-transform: uppercase;
    font-weight: 500;
    border: 1px solid darkcyan;
    border-radius: 50px;
    margin: 0 auto;
    padding: 5px 20px;
    width: 280px;
    background-color: darkcyan;
}

}
.box input[type="text"],
.box input[type="email"],
    textarea {
    background: none;
    display: block;
    margin: 20px auto;
    text-align: center;
    padding: 15px 10px;
    width: 200px;
    border: 1px solid turquoise;
    color: darkturquoise;
    border-radius: 24px;
    outline: none;

  }
.box input[type="text"]:focus,
.box input[type="email"]:focus,
textarea:focus {
    transition: 0.25s;
    width: 280px;
    background-color: rgba(106, 255, 240, 0.476) ;
    border-color: rgb(0, 92, 92);
  }
.box input[type="submit"]{
    background: none;
    display: block;
    margin: 0 auto;
    text-align: center;
    border: 1px solid turquoise;
    padding: 12px 40px;
    width: 200px;
    outline: none;
    border-radius: 24px;
    cursor: pointer;
    transition: 0.25s;
  }

.box input[type="submit"]:hover {
    background-color: darkturquoise;
    transition: 0.25s;
  }

*/
/* ***************** section - Contact *********************/
/*
.contact-left-side {

  width: 60%;
  text-decoration: none;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
  text-shadow: 2px 2px 5px rgb(96, 96, 96);
  border: 1px solid red;
  }
.contact-left-side p {
  padding:  5px;
  }
.contact-left-side h1 {
  margin-top: 5%;
  font-family: "Send Flowers"; 
  color: #004279;
  text-shadow: 2px 2px 5px rgb(96, 96, 96);
  padding:  5px;
  font-size: 5.6vh;
  }

.contact-right-side .g-maps{
  width: 45%;
  border: 1px solid red;
  }


/* ***********google map********* */
/*
.mapouter{

  display: flex;
  text-align:right;
  height:300px;
  width:300px;
  }

.gmap_canvas {
  overflow:hidden;
  background:none!important;
  height:300px;
  width:300px;
  }


.form-result {
  width: 80%;
  margin: 0 auto;
  padding: 10px;
  margin-bottom: 20px;
  }

.success {
  color: #ffffff;
  background-color: #007d81;
  border-radius: 3px;
  }

.error {
  color: #ffffff;
  background-color: #72040d;
  }