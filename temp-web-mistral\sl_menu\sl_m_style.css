
/* ************* Navigation menu ***************** */

#logo_nav_txt {
  
    position: absolute;
    left: 50%;
    transform: translate(-65%,-30%);
    font-family: "Send Flowers"; 
    font-size: 45px;
    color: #fff;
    text-shadow: 2px 2px 10px rgb(255, 255, 255);
}

#calling-phone-number-nav {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-65%, 20%);
  text-decoration: none;
  font-family: Roboto Flex;
  font-size: 25px;
  color: mediumturquoise;
  text-shadow: 2px 2px 10px rgb(255, 255, 255);
  letter-spacing: 1.5px;

}

#logo-flower-nav {
  width: 100px;
  position: relative;
  /* top: 50%; */
  left: 50%;
  transform: translate(95%, 10%);
}


section.top-nav {
  position: fixed;
}

.navig-menu li a:link,
.navig-menu li a:visited {
  color: #fff;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 18px;
  text-decoration: none;
  border-bottom: 3px solid transparent;
}

.navig-menu li a:hover,
.navig-menu li a:active {
  transition: border-bottom 0.5s;
  border-bottom: 3px solid rgb(7, 174, 174);
}


.top-nav {
  display: flex;
  position: fixed;
  z-index: 1000;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background: #053981;
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  color: #ffffff;
  height: 100px;
  padding: 1em;
  border-bottom: 1px solid rgb(1 255 200);
}

/* Kontejner pro logo a telefon na levé straně */
.nav-left-content {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

/* Menu na pravé straně */
.nav-right-content {
  display: flex;
  align-items: center;
  position: relative;
}


.menu {
  display: flex;
  flex-direction: row;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.menu > li {
  margin: 0 1rem;
  overflow: hidden;
}

.menu-button-container {
  display: none;
  height: 100%;
  width: 30px;
  cursor: pointer;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#menu-toggle {
  display: none;
}

.menu-button,
.menu-button::before,
.menu-button::after {
  display: block;
  background-color: #fff;
  position: absolute;
  height: 4px;
  width: 30px;
  transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
  border-radius: 2px;
}

.menu-button::before {
  content: "";
  margin-top: -8px;
}

.menu-button::after {
  content: "";
  margin-top: 8px;
}

#menu-toggle:checked + .menu-button-container .menu-button::before {
  margin-top: 0px;
  transform: rotate(405deg);
}

#menu-toggle:checked + .menu-button-container .menu-button {
  background: rgba(255, 255, 255, 0);
}

#menu-toggle:checked + .menu-button-container .menu-button::after {
  margin-top: 0px;
  transform: rotate(-405deg);
}

 @media (max-width: 768px) {
  .menu-button-container {
    display: flex;
    margin-left: auto;
    margin-right: 0;
    }

  .nav-right-content {
    justify-content: flex-end;
    position: relative;
  }

  /* Skryje desktop menu v mobilním režimu */
  .menu {
    display: none;
  }

  #calling-phone-number-nav {
    margin: 0 0 0 0px;
    font-size: 14px;
  }




  /* Mobilní menu se zobrazí při zaškrtnutí checkboxu */
  #menu-toggle:checked ~ .menu {
    display: flex;
    position: fixed;
    top: 80px;
    right: 0;
    left: 0;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    z-index: 999;
    background-color: rgba(34, 34, 34, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }

  #menu-toggle ~ .menu li {
    height: 0;
    margin: 0;
    padding: 0;
    border: 0;
    transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
  }

  #menu-toggle:checked ~ .menu li {
    border: 1px solid #555;
    height: 3em;
    padding: 0.8em;
    transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
    margin: 2px 10px;
    border-radius: 5px;
  }

  .menu > li {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0.8em 1em;
    width: calc(100% - 20px);
    color: white;
    background-color: rgba(34, 34, 34, 0.9);
    font-size: 16px;
    font-weight: 500;
  }

  .menu > li:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  .menu > li:not(:last-child) {
    border-bottom: none;
  }
}

/* Tablet responzivita */
@media (max-width: 1024px) and (min-width: 769px) {
  .menu-button-container {
    display: flex;
    margin-left: auto;
    margin-right: 0;
  }

  .menu {
    display: none;
  }

  #menu-toggle:checked ~ .menu {
    display: flex;
    position: fixed;
    top: 80px;
    right: 0;
    left: 0;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    z-index: 999;
    background-color: rgba(34, 34, 34, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }
}

/* Velmi malé obrazovky */
@media (max-width: 480px) {
  #calling-phone-number-nav {
    font-size: 12px;
    margin: 0;
  }

  .menu-button-container {
    margin-right: 10px;
  }

  .menu > li {
    font-size: 14px;
    padding: 0.6em 0.8em;
  }
}