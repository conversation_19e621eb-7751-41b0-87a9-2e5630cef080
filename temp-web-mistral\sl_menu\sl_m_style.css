
/* ************* Navigation menu ***************** */

#logo_nav_txt {
    position: relative;
    font-family: "Send Flowers";
    font-size: 40px;
    color: #fff;
    text-shadow: 2px 2px 10px rgb(255, 255, 255);
    margin-bottom: 20px;
    padding: 0;
    white-space: nowrap;
}

#calling-phone-number-nav {
  position: absolute;
  top: 65%;
  left: 0;
  text-decoration: none;
  font-family: Roboto Flex;
  font-size: 22px;
  color: mediumturquoise;
  text-shadow: 2px 2px 10px rgb(255, 255, 255);
  letter-spacing: 1.5px;
  margin: 0;
  line-height: 1.2px;
  white-space: nowrap;
}

#logo-flower-nav {
  width: 100px;
  position: relative;
  margin-left: 15px;
  flex-shrink: 0;
}


section.top-nav {
  position: fixed;
}

.navig-menu li a:link,
.navig-menu li a:visited {
  color: #fff;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 18px;
  text-decoration: none;
  border-bottom: 3px solid transparent;
}

.navig-menu li a:hover,
.navig-menu li a:active {
  transition: border-bottom 0.5s;
  border-bottom: 3px solid rgb(7, 174, 174);
}


.top-nav {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background: #053981;
    /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  color: #ffffff;
  height: 100px;
  padding: 1em 40px;
  border-bottom: 1px solid rgb(1 255 200);
  box-sizing: border-box;
}

/* Kontejner pro logo a telefon na levé straně */
.nav-left-content {
  display: flex;
  align-items: center;
  flex: 1;
  position: relative;
}

/* Logo blok - obaluje logo text, květinu a telefon */
.logo-block {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  height: 100%;
}

/* Menu na pravé straně */
.nav-right-content {
  display: flex;
  align-items: center;
  position: relative;
}


.menu {
  display: flex;
  flex-direction: row;
  list-style-type: none;
  margin: 0;
  padding: 0;
}

.menu > li {
  margin: 0 1rem;
  overflow: hidden;
}

.menu-button-container {
  display: none;
  height: 100%;
  width: 30px;
  cursor: pointer;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

#menu-toggle {
  display: none;
}

.menu-button,
.menu-button::before,
.menu-button::after {
  display: block;
  background-color: #fff;
  position: absolute;
  height: 4px;
  width: 30px;
  transition: transform 400ms cubic-bezier(0.23, 1, 0.32, 1);
  border-radius: 2px;
}

.menu-button::before {
  content: "";
  margin-top: -8px;
}

.menu-button::after {
  content: "";
  margin-top: 8px;
}

#menu-toggle:checked + .menu-button-container .menu-button::before {
  margin-top: 0px;
  transform: rotate(405deg);
}

#menu-toggle:checked + .menu-button-container .menu-button {
  background: rgba(255, 255, 255, 0);
}

#menu-toggle:checked + .menu-button-container .menu-button::after {
  margin-top: 0px;
  transform: rotate(-405deg);
}

/* Nad 1030px - zobraz horizontální menu, skryj hamburger */
@media (min-width: 1030px) {
  .menu-button-container {
    display: none;
  }

  .menu {
    display: flex !important;
  }
}

/* Pod 1030px - skryj horizontální menu, zobraz hamburger */
@media (max-width: 1029px) {
  .menu-button-container {
    display: flex;
    margin-left: auto;
  }

  .nav-right-content {
    justify-content: flex-end;
    position: relative;
  }

  /* Skryje desktop menu pod 1030px */
  .menu {
    display: none;
  }
}

/* Tablet a mobilní úpravy */
@media (max-width: 768px) {
  #calling-phone-number-nav {
    margin: 0 0 0 0px;
    font-size: 16px;
  }




  /* Mobilní menu se zobrazí při zaškrtnutí checkboxu */
  #menu-toggle:checked ~ .menu {
    display: flex;
    position: fixed;
    top: 100px;
    right: 0;
    left: 0;
    flex-direction: column;
    width: 100%;
    justify-content: flex-start;
    align-items: center;
    z-index: 9998;
    background-color: rgba(34, 34, 34, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
  }

  #menu-toggle ~ .menu li {
    height: 0;
    margin: 0;
    padding: 0;
    border: 0;
    transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
  }

  #menu-toggle:checked ~ .menu li {
    border: 1px solid #555;
    height: 3em;
    padding: 0.8em;
    transition: height 400ms cubic-bezier(0.23, 1, 0.32, 1);
    margin: 2px 10px;
    border-radius: 5px;
  }

  .menu > li {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0.8em 1em;
    width: calc(100% - 20px);
    color: white;
    background-color: rgba(34, 34, 34, 0.9);
    font-size: 16px;
    font-weight: 500;
  }

  .menu > li:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  .menu > li:not(:last-child) {
    border-bottom: none;
  }
}

/* Tablet responzivita - již pokryto hlavním breakpointem 1029px */
/* Tento breakpoint je nyní redundantní, protože 1029px pokrývá i tablety */

/* Střední mobilní obrazovky */
@media (max-width: 640px) {
  .logo-block {
    flex-direction: column;
    align-items: flex-start;
    height: auto;
  }

  #logo_nav_txt {
    font-size: 33px;
  }

  #calling-phone-number-nav {
    position: relative;
    top: auto;
    left: auto;
    font-size: 14px;
    margin-top: 2px;
  }

  #logo-flower-nav {
    width: 60px;
    margin-left: 10px;
    margin-top: -10px;
  }
}

/* Velmi malé obrazovky */
@media (max-width: 480px) {
  .top-nav {
    padding: 1em 30px;
  }

  .logo-block {
    flex-direction: column;
    align-items: flex-start;
  }

  #logo_nav_txt {
    font-size: 28px;
  }

  #calling-phone-number-nav {
    position: relative;
    top: auto;
    left: auto;
    font-size: 12px;
    margin-top: 2px;
  }

  #logo-flower-nav {
    width: 50px;
    margin-left: 8px;
    margin-top: -8px;
  }

  .menu > li {
    font-size: 14px;
    padding: 0.6em 0.8em;
  }
}