/* ===== MODERN RESPONSIVE QUERIES FOR PENSION MISTRAL ===== */

/* ===== UTILITY CLASSES ===== */
.empty-space-fifty-px {
    height: 50px;
}

.empty-space-ten-px {
    height: 10px;
}

/* Responsive spacing */
@media (max-width: 768px) {
    .empty-space-fifty-px {
        height: 30px;
    }

    .empty-space-ten-px {
        height: 8px;
    }
}

@media (max-width: 480px) {
    .empty-space-fifty-px {
        height: 20px;
    }

    .empty-space-ten-px {
        height: 5px;
    }
}

/* ===== TYPOGRAPHY RESPONSIVE ===== */
.title-main {
    text-align: center;
    margin: 2rem auto;
    max-width: 90%;
}

.title-main h3 {
    font-family: "Send Flowers", cursive;
    color: #004279;
    text-shadow: 2px 2px 5px rgba(96, 96, 96, 0.5);
    font-size: 3rem;
    margin: 0;
    padding: 0 1rem;
}

/* ===== CONTENT SECTIONS ===== */
.body-section-title {
    padding: 2rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.post-content-pension-see {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.pension-mistral-p p {
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    text-align: justify;
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Large Desktop */
@media (min-width: 1200px) {
    .title-main h3 {
        font-size: 3.5rem;
    }

    .pension-mistral-p p {
        font-size: 1.1rem;
    }
}

/* Desktop */
@media (max-width: 1199px) {
    .post-content-pension-see {
        padding: 0 1.5rem;
    }
}

/* Tablet Landscape */
@media (max-width: 1024px) {
    .title-main h3 {
        font-size: 2.8rem;
    }

    .body-section-title {
        padding: 1.5rem 0;
    }

    .post-content-pension-see {
        padding: 0 1rem;
    }
}

/* Tablet Portrait */
@media (max-width: 768px) {
    .title-main h3 {
        font-size: 2.2rem;
        padding: 0 0.5rem;
    }

    .body-section-title {
        padding: 1rem 0;
    }

    .post-content-pension-see {
        padding: 0 0.8rem;
    }

    .pension-mistral-p p {
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 1.2rem;
    }
}

/* Mobile */
@media (max-width: 480px) {
    .title-main {
        margin: 1rem auto;
    }

    .title-main h3 {
        font-size: 1.8rem;
        padding: 0 0.3rem;
    }

    .body-section-title {
        padding: 0.8rem 0;
    }

    .post-content-pension-see {
        padding: 0 0.5rem;
    }

    .pension-mistral-p p {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 1rem;
        text-align: left;
    }
}

/* Very Small Mobile */
@media (max-width: 360px) {
    .title-main h3 {
        font-size: 1.6rem;
    }

    .pension-mistral-p p {
        font-size: 0.85rem;
    }

    .post-content-pension-see {
        padding: 0 0.3rem;
    }
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Reduce animations on low-end devices */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .title-main h3 {
        text-shadow: 1px 1px 3px rgba(96, 96, 96, 0.3);
    }
}